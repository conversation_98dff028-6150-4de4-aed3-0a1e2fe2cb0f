import 'package:flutter/material.dart';
import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/widgets/count_down.dart';
import 'package:fusion/gen/colors.gen.dart';

/// 交易保护中状态示例
class TradeProtectionExample extends StatelessWidget {
  const TradeProtectionExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('交易保护中状态示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '订单状态示例：',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildOrderStateCard(OrderState.tradeProtection, 3600), // 1小时倒计时
            const SizedBox(height: 16),
            const Text(
              '状态说明：',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• 状态码：10'),
            const Text('• 显示文本：交易保护中'),
            const Text('• 颜色：橙色'),
            const Text('• 支持倒计时：是'),
            const Text('• 可用操作：无'),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderStateCard(OrderState state, int countdown) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  state.filterOptionText,
                  style: TextStyle(
                    color: state.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (state.enableCountdown && countdown > 0)
                  Countdown(
                    builder: (ctx, remaining, formatted) {
                      return Text(
                        formatted,
                        style: TextStyle(
                          color: state.textColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                    times: countdown,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '状态码: ${state.index}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            Text(
              '启用倒计时: ${state.enableCountdown ? "是" : "否"}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            Text(
              '可用操作: ${state.actionValue?.actionText ?? "无"}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
